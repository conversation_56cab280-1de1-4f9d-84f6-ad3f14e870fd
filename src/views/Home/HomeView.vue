<template>
  <MainLayout>
    <div class="home-view">
      <van-swipe-cell>
        <van-cell :border="false" title="单元格" value="内容" />
        <template #right>
          <van-button square type="primary" text="编辑" @click="onEditClick" />
          <van-button square type="danger" text="删除" @click="onDeleteClick" />
        </template>
      </van-swipe-cell>
      <van-loading />
      <van-button @click="copy">Copy!</van-button>
      <van-button type="primary" @click="showGiftPopup" style="margin: 16px 0;">查看礼品明细</van-button>
      <div class="font">123213312</div>



      <!-- 路由跳转测试区域 -->
      <div class="route-test-area">
        <h2>路由跳转测试</h2>
        <div class="route-links">
          <van-button type="primary" @click="$router.push('/home')" style="margin: 5px">首页</van-button>
          <van-button type="primary" @click="$router.push('/goodslist/61072?distri_biz_code=ygjd')" style="margin: 5px">商品列表页面</van-button>
          <van-button type="primary" @click="$router.push('/addr/add?from=home')" style="margin: 5px">地址添加</van-button>
          <van-button type="primary" @click="$router.push('/addr/edit?id=1')" style="margin: 5px">地址编辑</van-button>
          <van-button type="primary" @click="$router.push('/addr/list')" style="margin: 5px">地址列表</van-button>
          <van-button type="primary" @click="$router.push('/cart')" style="margin: 5px">购物车</van-button>
          <van-button type="primary" @click="$router.push('/user/order/detail?orderId=123')"
            style="margin: 5px">订单详情</van-button>
          <van-button type="primary" @click="$router.push('/user/order/search?keyword=test')"
            style="margin: 5px">订单搜索</van-button>
          <van-button type="primary" @click="$router.push('/user/order/recycle')" style="margin: 5px">订单回收站</van-button>
          <van-button type="primary" @click="$router.push('/orderconfirm?goodsId=456')"
            style="margin: 5px">订单确认</van-button>
          <van-button type="primary" @click="$router.push('/user/order/list?status=1')"
            style="margin: 5px">订单列表</van-button>
          <van-button type="primary" @click="$router.push('/category/2')" style="margin: 5px">分类页面</van-button>
          <van-button type="primary" @click="$router.push('/user')" style="margin: 5px">用户中心</van-button>
          <van-button type="primary" @click="$router.push('/search?keyword=手机')" style="margin: 5px">搜索页面</van-button>
          <van-button type="primary" @click="$router.push('/search/list?keyword=电脑')"
            style="margin: 5px">搜索结果列表</van-button>
          <van-button type="primary" @click="$router.push('/user/wish')" style="margin: 5px">心愿商品列表</van-button>
          <van-button type="primary" @click="$router.push('/user/wish/list?id=789')"
            style="margin: 5px">心愿商品详情</van-button>
          <van-button type="primary" @click="$router.push('/wo-after-sales-entry')"
                      style="margin: 5px">售后入口页面</van-button>
          <van-button type="primary" @click="$router.push('/wo-after-sales-detail?afterSaleId=20250520174636660269&type=1')"
                      style="margin: 5px">售后详情页面</van-button>
          <van-button type="primary" @click="$router.push('/wo-after-sales-refund?supplierSubOrderId=123&orderPrice=10000&skuNum=1')" style="margin: 5px">售后退款页面</van-button>
          <van-button type="primary" @click="$router.push('/wo-after-sales-return-good?supplierSubOrderId=123&orderPrice=10000&skuNum=1')" style="margin: 5px">售后退货申请</van-button>
          <van-button type="primary" @click="$router.push('/wo-after-sales-express?orderId=17464946729708377514&applySaleApplyId=456')" style="margin: 5px">售后物流查询(需要修改)</van-button>
          <van-button type="primary" @click="$router.push('/wo-after-sales-logistics-info?applySaleApplyId=456')" style="margin: 5px">物流信息填写</van-button>
        </div>
      </div>



      <h2>瀑布流布局兼容性示例</h2>
      <Waterfall :list="filteredList" :breakpoints="breakpoints" :hasAroundGutter="false">
        <template #default="{ item, url, index }">
          <div class="masonry-item" @click="handleItemClick(item)">
            <div class="card">
              <div class="image-container">
                <LazyImg :url="item.imageUrl" />
              </div>
              <div class="card-content">
                <h3>{{ item.title }}</h3>
                <p>{{ item.description }}</p>
                <div class="card-footer">
                  <span>{{ item.date }}</span>
                  <van-button size="small" type="primary" @click.stop="copyText(item.title)">复制标题</van-button>
                </div>
              </div>
            </div>
          </div>
        </template>
      </Waterfall>

      <!-- 加载状态 -->
      <div class="loading-state" v-if="loading">
        <van-loading type="spinner" color="#1989fa" />
        <p>加载中...</p>
      </div>

      <!-- 加载更多按钮 -->
      <div class="load-more" v-if="!loading && !noMoreData">
        <van-button type="primary" block @click="loadMoreItems">加载更多</van-button>
      </div>

      <!-- 无更多数据提示 -->
      <div class="no-more-data" v-if="noMoreData">
        <p>没有更多数据了</p>
      </div>


    </div>
    <AddressQuickSelectionPopup :visible="showAddressPopup" @close="showAddressPopup = false" />
    <GiftDisplayPopup v-model:visible="giftPopupVisible" :gift-list="giftList" />
  </MainLayout>
</template>

<script setup>
import { LazyImg, Waterfall } from 'vue-waterfall-plugin-next'
import 'vue-waterfall-plugin-next/dist/style.css'
import { onMounted, getCurrentInstance, ref, computed } from 'vue'
import { useAlert } from '@/composables'
import useClipboard from 'vue-clipboard3'
import { showToast } from "vant";
import AddressQuickSelectionPopup from '@components/Address/AddressQuickSelectionPopup.vue'
import GiftDisplayPopup from '@components/Common/GiftDisplayPopup.vue'
import MainLayout from '@components/Common/MainLayout/MainLayout.vue'
import { getDefaultBreakpoints } from '@/config/responsive.js'

const { proxy } = getCurrentInstance()
const { toClipboard } = useClipboard()
const $alert = useAlert()
const breakpoints = ref(getDefaultBreakpoints())


// 礼品弹窗相关
const giftPopupVisible = ref(false)
const giftList = ref([
  {
    id: 1,
    name: '精美保温杯维达(Vinda)卷纸蓝色经典3层卫生纸卷纸蓝卷纸蓝色卫经典卫经的维达(Vinda)卷纸蓝色经典3层卫生纸卷纸蓝卷纸蓝色卫经典卫经的维达(Vinda)卷纸蓝色经典3层卫生纸卷纸蓝卷纸蓝色卫经典卫经的维达(Vinda)卷纸蓝色经典3层卫生纸卷纸蓝卷纸蓝色卫经典卫经的维达(Vinda)卷纸蓝色经典3层卫生纸卷纸蓝卷纸蓝色卫经典卫经的维达(Vinda)卷纸蓝色经典3层卫生纸卷纸蓝卷纸蓝色卫经典卫经的',
    description: '304不锈钢内胆，24小时保温保冷',
    imageUrl: 'https://picsum.photos/seed/gift1/200/200',
    quantity: 1,
    condition: '消费满500元'
  },
  {
    id: 2,
    name: '多功能背包',
    description: '大容量设计，防水面料，适合户外旅行',
    imageUrl: 'https://picsum.photos/seed/gift2/200/200',
    quantity: 1,
    condition: '消费满800元'
  },
  {
    id: 3,
    name: '无线蓝牙耳机',
    description: '高清音质，长续航，舒适佩戴',
    imageUrl: 'https://picsum.photos/seed/gift3/200/200',
    quantity: 1,
    condition: '消费满1000元'
  },
  {
    id: 4,
    name: '精美笔记本',
    description: '优质纸张，精致装帧，商务办公必备',
    imageUrl: 'https://picsum.photos/seed/gift4/200/200',
    quantity: 2,
    condition: '消费满300元'
  }
])

// 显示礼品弹窗
const showGiftPopup = () => {
  giftPopupVisible.value = true
}



const showAddressPopup = ref(false)
const selectedId = ref('5')
const addressList = ref([
  {
    id: '1',
    name: '张三',
    phone: '13800138000',
    region: '北京市朝阳区',
    detailAddress: '三里屯SOHO 5号楼3单元801室',
    isDefault: false
  },
  {
    id: '2',
    name: '张三',
    phone: '13800138000',
    region: '北京市朝阳区',
    detailAddress: '三里屯SOHO 5号楼3单元801室',
    isDefault: false
  },
  {
    id: '3',
    name: '张三',
    phone: '13800138000',
    region: '北京市朝阳区',
    detailAddress: '三里屯SOHO 5号楼3单元801室',
    isDefault: false
  },
  {
    id: '4',
    name: '张三',
    phone: '13800138000',
    region: '北京市朝阳区',
    detailAddress: '三里屯SOHO 5号楼3单元801室',
    isDefault: false
  },
  {
    id: '5',
    name: '张三',
    phone: '13800138000',
    region: '北京市朝阳区',
    detailAddress: '三里屯SOHO 5号楼3单元801室',
    isDefault: true
  },
  {
    id: '6',
    name: '张三',
    phone: '13800138000',
    region: '北京市朝阳区',
    detailAddress: '三里屯SOHO 5号楼3单元801室',
    isDefault: false
  }
])
// 筛选相关数据
const filterValue = ref(0)
// 状态控制
const loading = ref(false)
const noMoreData = ref(false)
// 创建图片列表数据
const imageList = ref([
  {
    id: 1,
    title: '自然风景',
    description: '美丽的山水风光，令人心旷神怡',
    imageUrl: 'https://picsum.photos/seed/img1/300/400',
    date: '2023-05-15'
  },
  {
    id: 2,
    title: '城市夜景',
    description: '繁华都市的灯火辉煌',
    imageUrl: 'https://picsum.photos/seed/img2/200/300',
    date: '2023-06-20'
  },
  {
    id: 3,
    title: '花卉特写',
    description: '绽放的花朵，生机盎然',
    imageUrl: 'https://picsum.photos/seed/img3/480/320',
    date: '2023-07-08'
  },
  {
    id: 4,
    title: '建筑艺术',
    description: '独特的建筑设计，展现人类智慧',
    imageUrl: 'https://picsum.photos/seed/img4/350/500',
    date: '2023-08-12'
  },
  {
    id: 5,
    title: '美食佳肴',
    description: '色香味俱全的美食盛宴',
    imageUrl: 'https://picsum.photos/seed/img5/250/380',
    date: '2023-09-05'
  },
  {
    id: 6,
    title: '动物世界',
    description: '自然界中的各种生命',
    imageUrl: 'https://picsum.photos/seed/img6/400/250',
    date: '2023-10-18'
  },
  {
    id: 7,
    title: '科技产品',
    description: '现代科技的最新成果',
    imageUrl: 'https://picsum.photos/seed/img7/320/420',
    date: '2023-11-30'
  },
  {
    id: 8,
    title: '艺术创作',
    description: '富有创意的艺术表现形式',
    imageUrl: 'https://picsum.photos/seed/img8/380/300',
    date: '2023-12-25'
  },
  {
    id: 9,
    title: '旅行探险',
    description: '探索世界各地的奇妙之旅',
    imageUrl: 'https://picsum.photos/seed/img9/420/280',
    date: '2024-01-15'
  },
  {
    id: 10,
    title: '运动瞬间',
    description: '激烈比赛中的精彩瞬间',
    imageUrl: 'https://picsum.photos/seed/img10/350/450',
    date: '2024-02-08'
  }
])

// 根据筛选条件过滤列表
const filteredList = computed(() => {
  if (filterValue.value === 0) {
    return imageList.value
  } else if (filterValue.value === 1) {
    return imageList.value.filter(item => item.date.startsWith('2023'))
  } else if (filterValue.value === 2) {
    return imageList.value.filter(item => item.date.startsWith('2024'))
  }
  return imageList.value
})

// 图片加载失败处理
const handleImageError = (event, index) => {
  // 设置默认图片
  event.target.src = `https://via.placeholder.com/300x400?text=图片${index + 1}加载失败`;
  console.error(`图片加载失败: ${imageList.value[index].imageUrl}`);
}

// 加载更多图片
const loadMoreItems = async () => {
  if (loading.value || noMoreData.value) return;

  loading.value = true;

  // 模拟加载更多数据
  try {
    // 模拟网络请求延迟
    await new Promise(resolve => setTimeout(resolve, 1500));

    const newItems = [
      {
        id: 11,
        title: '历史文化',
        description: '人类文明的历史印记',
        imageUrl: 'https://picsum.photos/seed/img11/300/350',
        date: '2024-03-05'
      },
      {
        id: 12,
        title: '时尚潮流',
        description: '引领时代的时尚元素',
        imageUrl: 'https://picsum.photos/seed/img12/320/400',
        date: '2024-03-18'
      },
      {
        id: 13,
        title: '创意设计',
        description: '独特创意的视觉体验',
        imageUrl: 'https://picsum.photos/seed/img13/350/300',
        date: '2024-04-01'
      },
      {
        id: 14,
        title: '奇妙自然',
        description: '大自然的神奇魅力',
        imageUrl: 'https://picsum.photos/seed/img14/400/300',
        date: '2024-04-15'
      }
    ];

    // 添加到现有列表
    imageList.value = [...imageList.value, ...newItems];
    showToast('已加载更多内容');

    // 假设这是最后一批数据
    noMoreData.value = true;
  } catch (error) {
    console.error('加载更多失败:', error);
    showToast('加载失败，请重试');
  } finally {
    loading.value = false;
  }
}

// 处理点击事件
const handleItemClick = (item) => {
  showToast(`您点击了: ${item.title}`);
}

// 复制文本
const copyText = async (text) => {
  try {
    await toClipboard(text);
    showToast('复制成功');
  } catch (e) {
    console.error(e);
    showToast('复制失败');
  }
}

const onEditClick = () => {
  proxy.$alert({
    message: '确定要删除吗？',
    showCancelButton: true,
    onConfirmCallback: () => {
      showToast('删除成功');
    },
    onCancelCallback: () => {
      showToast('取消删除');
    }
  });
};

const onDeleteClick = () => {
  $alert({
    message: '确定要删除吗？',
    showCancelButton: true,
    onConfirmCallback: () => {
      showToast('删除成功');
    },
    onCancelCallback: () => {
      showToast('取消删除');
    }
  });
}

const copy = async () => {
  try {
    showAddressPopup.value = true
    await toClipboard('Any text you like')
    console.log('Copied to clipboard')
  } catch (e) {
    console.error(e)
  }
}

onMounted(async () => {
  // try {
  //   const data = {
  //     bizCode: 'ygjd',
  //     categoryId: 61072
  //   }
  //
  //   // 确保请求头设置正确
  //   const response = await skuPageListBrandList(data)
  //
  //   console.log('请求响应:', response); // 添加日志确认请求是否成功
  // } catch (err) {
  //   console.error('请求出错:', err);
  // } finally {
  // }
})
</script>

<style lang="less" scoped>
.font {
  font-family: D-DIN-PRO-SemiBold;
  font-size: 17px;
  color: #FF780A;
  line-height: 16px;
  font-weight: 600;
}

.home-view {
  overflow: auto;
  height: 100%;
  padding: 16px;

  h2 {
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: bold;
    text-align: center;
  }
}

.filter-bar {
  margin-bottom: 16px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.masonry-container {
  width: 100%;
}

.masonry-item {
  break-inside: avoid;
  cursor: pointer;
}

.card {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s, box-shadow 0.3s;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  }

  .image-container {
    width: 100%;
    overflow: hidden;
    position: relative;

    img {
      width: 100%;
      height: auto;
      object-fit: cover;
      transition: transform 0.5s;
    }

    .image-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      color: white;
      display: flex;
      justify-content: center;
      align-items: center;
      opacity: 0;
      transition: opacity 0.3s;
    }

    &:hover {
      img {
        transform: scale(1.05);
      }

      .image-overlay {
        opacity: 1;
      }
    }
  }

  .card-content {
    padding: 12px;

    h3 {
      margin: 0 0 8px;
      font-size: 16px;
      font-weight: bold;
    }

    p {
      margin: 0 0 12px;
      font-size: 14px;
      color: #666;
      line-height: 1.4;
    }

    .card-footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 12px;
      color: #999;
    }
  }
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px 0;

  p {
    margin-top: 10px;
    color: #999;
    font-size: 14px;
  }
}

.load-more {
  margin-top: 20px;
  margin-bottom: 30px;
}

.no-more-data {
  text-align: center;
  color: #999;
  padding: 20px 0;
  font-size: 14px;
}
</style>
